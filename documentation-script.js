// Documentation Website JavaScript
document.addEventListener("DOMContentLoaded", function () {
  // Initialize all functionality
  initializeNavigation();
  initializeSidebar();
  initializeMobileMenu();
  initializePDFExport();
  initializeSmoothScrolling();
  initializeActiveSectionTracking();
  initializeAnimations();
});

// Navigation functionality
function initializeNavigation() {
  const navLinks = document.querySelectorAll(".nav-link");
  const sections = document.querySelectorAll(".content-section");

  // Handle navigation clicks
  navLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();

      // Remove active class from all links
      navLinks.forEach((l) => l.classList.remove("active"));

      // Add active class to clicked link
      this.classList.add("active");

      // Get target section
      const targetId = this.getAttribute("href").substring(1);
      const targetSection = document.getElementById(targetId);

      if (targetSection) {
        // Smooth scroll to target
        targetSection.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });

        // Update URL without page reload
        history.pushState(null, null, `#${targetId}`);
      }
    });
  });
}

// Sidebar functionality
function initializeSidebar() {
  const sidebar = document.getElementById("sidebar");
  const sidebarToggle = document.getElementById("sidebarToggle");
  const mainContent = document.getElementById("mainContent");

  // Toggle sidebar on mobile
  if (sidebarToggle) {
    sidebarToggle.addEventListener("click", function () {
      sidebar.classList.toggle("active");
      this.classList.toggle("active");

      // Update toggle icon
      const icon = this.querySelector("i");
      if (sidebar.classList.contains("active")) {
        icon.className = "fas fa-times";
      } else {
        icon.className = "fas fa-bars";
      }
    });
  }

  // Close sidebar when clicking outside on mobile
  document.addEventListener("click", function (e) {
    if (window.innerWidth <= 1024) {
      if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
        sidebar.classList.remove("active");
        if (sidebarToggle) {
          sidebarToggle.classList.remove("active");
          const icon = sidebarToggle.querySelector("i");
          icon.className = "fas fa-bars";
        }
      }
    }
  });

  // Handle window resize
  window.addEventListener("resize", function () {
    if (window.innerWidth > 1024) {
      sidebar.classList.remove("active");
      if (sidebarToggle) {
        sidebarToggle.classList.remove("active");
        const icon = sidebarToggle.querySelector("i");
        icon.className = "fas fa-bars";
      }
    }
  });
}

// Mobile menu functionality
function initializeMobileMenu() {
  const mobileMenuToggle = document.getElementById("mobileMenuToggle");
  const sidebar = document.getElementById("sidebar");

  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener("click", function () {
      sidebar.classList.toggle("active");

      // Update toggle icon
      const icon = this.querySelector("i");
      if (sidebar.classList.contains("active")) {
        icon.className = "fas fa-times";
      } else {
        icon.className = "fas fa-bars";
      }
    });
  }
}

// PDF Export functionality
function initializePDFExport() {
  const pdfExportBtn = document.getElementById("pdfExportBtn");

  if (pdfExportBtn) {
    pdfExportBtn.addEventListener("click", function () {
      // Show loading overlay
      showPDFLoader();

      // Show loading state on button
      const originalText = this.innerHTML;
      this.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
      this.disabled = true;

      // Wait a bit for UI to update
      setTimeout(() => {
        generatePDF(originalText, this);
      }, 100);
    });
  }
}

// Show PDF loading overlay
function showPDFLoader() {
  // Create loader overlay
  const loader = document.createElement("div");
  loader.id = "pdf-loader";
  loader.innerHTML = `
    <div class="pdf-loader-content">
      <div class="pdf-loader-spinner">
        <i class="fas fa-file-pdf"></i>
        <div class="spinner-ring"></div>
      </div>
      <h3>Generating PDF...</h3>
      <p>Please wait while we create your documentation PDF</p>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
    </div>
  `;
  document.body.appendChild(loader);

  // Animate progress bar
  setTimeout(() => {
    const progressFill = loader.querySelector(".progress-fill");
    progressFill.style.width = "100%";
  }, 500);
}

// Hide PDF loading overlay
function hidePDFLoader() {
  const loader = document.getElementById("pdf-loader");
  if (loader) {
    loader.remove();
  }
}

// Generate PDF function using html2canvas + jsPDF
function generatePDF(originalText, button) {
  // Get main content first so it's available for all uses
  const mainContent = document.getElementById("mainContent");

  // Check if mainContent exists and has content
  if (!mainContent) {
    console.error("mainContent element not found!");
    hidePDFLoader();
    button.innerHTML =
      '<i class="fas fa-exclamation-triangle"></i> Error: Content not found';
    setTimeout(() => {
      button.innerHTML = originalText;
      button.disabled = false;
    }, 3000);
    return;
  }

  console.log("mainContent found:", mainContent);
  console.log("mainContent dimensions:", {
    scrollWidth: mainContent.scrollWidth,
    scrollHeight: mainContent.scrollHeight,
    offsetWidth: mainContent.offsetWidth,
    offsetHeight: mainContent.offsetHeight,
  });

  // Store and force white background for PDF rendering
  const originalMainBg = mainContent.style.background;
  mainContent.style.background = "#fff";

  // Animated elements to force visible for PDF
  const animatedSelectors = [
    ".feature-card",
    ".tech-item",
    ".report-card",
    ".export-type-card",
    ".filter-category",
    ".metric-card",
    ".auth-feature-card",
    ".role-card",
    ".control-card",
    ".measure-card",
    ".protection-feature-card",
  ];
  const animatedElements = Array.from(
    document.querySelectorAll(animatedSelectors.join(", "))
  );
  // Store original styles
  const animatedOriginals = animatedElements.map((el) => ({
    el,
    opacity: el.style.opacity,
    transform: el.style.transform,
  }));
  // Force visible
  animatedElements.forEach((el) => {
    el.style.opacity = "1";
    el.style.transform = "none";
  });

  // Also ensure all content sections are visible
  const contentSections = document.querySelectorAll(".content-section");
  contentSections.forEach((section) => {
    section.style.opacity = "1";
    section.style.transform = "none";
    section.style.display = "block";
    section.style.visibility = "visible";
  });

  // Hide sidebar and controls for PDF
  const sidebar = document.getElementById("sidebar");
  const headerControls = document.querySelector(".header-controls");
  const sectionNavs = document.querySelectorAll(".section-nav");
  const backToTopBtns = document.querySelectorAll(
    '.btn[onclick="scrollToTop()"]'
  );

  // Store original values
  const originalSidebarDisplay = sidebar.style.display;
  const originalHeaderControlsDisplay = headerControls.style.display;
  const originalSectionNavDisplays = Array.from(sectionNavs).map(
    (nav) => nav.style.display
  );
  const originalBackToTopDisplays = Array.from(backToTopBtns).map(
    (btn) => btn.style.display
  );

  // Hide elements for PDF
  sidebar.style.display = "none";
  headerControls.style.display = "none";
  sectionNavs.forEach((nav) => (nav.style.display = "none"));
  backToTopBtns.forEach((btn) => (btn.style.display = "none"));

  // mainContent is already declared above, do not redeclare
  const originalMarginLeft = mainContent.style.marginLeft;
  const originalWidth = mainContent.style.width;

  mainContent.style.marginLeft = "0";
  mainContent.style.width = "100%";

  // DEBUG: Highlight and log main content before PDF generation
  mainContent.style.outline = "4px solid red";
  console.log("[PDF DEBUG] mainContent.innerHTML:", mainContent.innerHTML);

  // Wait for layout to settle
  setTimeout(() => {
    // Configure html2canvas options
    const canvasOptions = {
      scale: 1.5,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      logging: true,
      width: mainContent.scrollWidth || mainContent.offsetWidth,
      height: mainContent.scrollHeight || mainContent.offsetHeight,
      scrollX: 0,
      scrollY: 0,
      windowWidth: 1200,
      windowHeight: window.innerHeight,
      onclone: function (clonedDoc) {
        console.log("Document cloned for PDF generation");
        // Ensure all content is visible in the clone
        const clonedMainContent = clonedDoc.getElementById("mainContent");
        if (clonedMainContent) {
          clonedMainContent.style.display = "block";
          clonedMainContent.style.visibility = "visible";
          clonedMainContent.style.opacity = "1";
          clonedMainContent.style.background = "#ffffff";

          // Force all text to be black for better PDF readability
          const allElements = clonedMainContent.querySelectorAll("*");
          allElements.forEach((el) => {
            const computedStyle = window.getComputedStyle(el);
            if (computedStyle.color && computedStyle.color !== "rgb(0, 0, 0)") {
              el.style.color = "#000000";
            }
            // Ensure backgrounds are visible
            if (
              el.style.backgroundColor === "transparent" ||
              !el.style.backgroundColor
            ) {
              el.style.backgroundColor = "inherit";
            }
          });
        }
      },
    };

    console.log("Starting html2canvas with options:", canvasOptions);
    html2canvas(mainContent, canvasOptions)
      .then((canvas) => {
        console.log("Canvas generated:", {
          width: canvas.width,
          height: canvas.height,
          dataURL: canvas.toDataURL().substring(0, 100) + "...",
        });

        // Check if canvas is empty
        if (canvas.width === 0 || canvas.height === 0) {
          throw new Error("Generated canvas is empty (0x0 dimensions)");
        }

        // Remove debug highlight
        mainContent.style.outline = "";
        // Restore main content background
        mainContent.style.background = originalMainBg;
        // Restore animated elements' original styles
        animatedOriginals.forEach(({ el, opacity, transform }) => {
          el.style.opacity = opacity;
          el.style.transform = transform;
        });

        // Create jsPDF instance
        const { jsPDF } = window.jspdf;
        if (!jsPDF) {
          throw new Error("jsPDF library not loaded");
        }
        const pdf = new jsPDF("p", "mm", "a4");

        // Calculate dimensions
        const imgWidth = 210; // A4 width in mm
        const pageHeight = 295; // A4 height in mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;
        let position = 0;

        // Add first page
        pdf.addImage(
          canvas.toDataURL("image/jpeg", 0.95),
          "JPEG",
          0,
          position,
          imgWidth,
          imgHeight
        );
        heightLeft -= pageHeight;

        // Add additional pages if needed
        while (heightLeft >= 0) {
          position = heightLeft - imgHeight;
          pdf.addPage();
          pdf.addImage(
            canvas.toDataURL("image/jpeg", 0.95),
            "JPEG",
            0,
            position,
            imgWidth,
            imgHeight
          );
          heightLeft -= pageHeight;
        }

        // Add page numbers
        const totalPages = pdf.internal.getNumberOfPages();
        for (let i = 1; i <= totalPages; i++) {
          pdf.setPage(i);
          pdf.setFontSize(10);
          pdf.setTextColor(128, 128, 128);
          pdf.text(`Page ${i} of ${totalPages}`, imgWidth - 20, pageHeight - 5);
        }

        // Save the PDF with fallback for security issues
        try {
          pdf.save("Mass_Premier_Courts_Documentation.pdf");
          console.log("PDF saved successfully");
        } catch (error) {
          console.warn("Direct save failed, trying alternative method:", error);
          // Alternative download method
          const pdfBlob = pdf.output("blob");
          const url = URL.createObjectURL(pdfBlob);
          const a = document.createElement("a");
          a.href = url;
          a.download = "Mass_Premier_Courts_Documentation.pdf";
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }

        // Success - restore everything
        restoreOriginalState(
          sidebar,
          headerControls,
          sectionNavs,
          backToTopBtns,
          mainContent,
          originalSidebarDisplay,
          originalHeaderControlsDisplay,
          originalSectionNavDisplays,
          originalBackToTopDisplays,
          originalMarginLeft,
          originalWidth,
          button,
          originalText
        );
        hidePDFLoader();
      })
      .catch((error) => {
        // Remove debug highlight
        mainContent.style.outline = "";
        // Restore main content background
        mainContent.style.background = originalMainBg;
        // Restore animated elements' original styles
        animatedOriginals.forEach(({ el, opacity, transform }) => {
          el.style.opacity = opacity;
          el.style.transform = transform;
        });
        console.error("PDF generation failed:", error);

        // Error - restore everything and show error
        restoreOriginalState(
          sidebar,
          headerControls,
          sectionNavs,
          backToTopBtns,
          mainContent,
          originalSidebarDisplay,
          originalHeaderControlsDisplay,
          originalSectionNavDisplays,
          originalBackToTopDisplays,
          originalMarginLeft,
          originalWidth,
          button,
          originalText
        );

        hidePDFLoader();

        // Show error message
        button.innerHTML =
          '<i class="fas fa-exclamation-triangle"></i> PDF Generation Failed';
        setTimeout(() => {
          button.innerHTML = originalText;
          button.disabled = false;
        }, 3000);
      });
  }, 500); // Wait for layout changes to take effect
}

// Restore original state function
function restoreOriginalState(
  sidebar,
  headerControls,
  sectionNavs,
  backToTopBtns,
  mainContent,
  originalSidebarDisplay,
  originalHeaderControlsDisplay,
  originalSectionNavDisplays,
  originalBackToTopDisplays,
  originalMarginLeft,
  originalWidth,
  button,
  originalText
) {
  sidebar.style.display = originalSidebarDisplay;
  headerControls.style.display = originalHeaderControlsDisplay;
  sectionNavs.forEach((nav, index) => {
    nav.style.display = originalSectionNavDisplays[index];
  });
  backToTopBtns.forEach((btn, index) => {
    btn.style.display = originalBackToTopDisplays[index];
  });
  mainContent.style.marginLeft = originalMarginLeft;
  mainContent.style.width = originalWidth;

  // Restore button state
  button.innerHTML = originalText;
  button.disabled = false;
}

// Smooth scrolling functionality
function initializeSmoothScrolling() {
  // Back to top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }

  // Make scrollToTop function globally available
  window.scrollToTop = scrollToTop;

  // Add scroll to top button functionality
  const scrollToTopButtons = document.querySelectorAll(
    '.btn[onclick="scrollToTop()"]'
  );
  scrollToTopButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();
      scrollToTop();
    });
  });
}

// Active section tracking
function initializeActiveSectionTracking() {
  const sections = document.querySelectorAll(".content-section");
  const navLinks = document.querySelectorAll(".nav-link");

  // Intersection Observer for tracking active sections
  const observerOptions = {
    root: null,
    rootMargin: "-20% 0px -70% 0px",
    threshold: 0,
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const currentId = entry.target.id;

        // Update navigation
        navLinks.forEach((link) => {
          link.classList.remove("active");
          if (link.getAttribute("href") === `#${currentId}`) {
            link.classList.add("active");
          }
        });

        // Update URL
        history.replaceState(null, null, `#${currentId}`);
      }
    });
  }, observerOptions);

  // Observe all sections
  sections.forEach((section) => {
    observer.observe(section);
  });

  // Handle initial page load with hash
  if (window.location.hash) {
    const targetSection = document.querySelector(window.location.hash);
    if (targetSection) {
      // Update active navigation
      navLinks.forEach((link) => {
        link.classList.remove("active");
        if (link.getAttribute("href") === window.location.hash) {
          link.classList.add("active");
        }
      });

      // Scroll to section
      setTimeout(() => {
        targetSection.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  }
}

// Animation functionality
function initializeAnimations() {
  // Intersection Observer for animations
  const animationObserver = new IntersectionObserver(
    function (entries) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = "1";
          entry.target.style.transform = "translateY(0)";
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    }
  );

  // Observe elements for animation (excluding content-card to prevent hiding)
  const animatedElements = document.querySelectorAll(
    ".tech-item, .feature-card, .report-card, .export-type-card, .filter-category, .metric-card, .auth-feature-card, .role-card, .control-card, .measure-card, .protection-feature-card"
  );
  animatedElements.forEach((el) => {
    el.style.opacity = "0";
    el.style.transform = "translateY(30px)";
    el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
    animationObserver.observe(el);
  });

  // Ensure all content cards are always visible
  const contentCards = document.querySelectorAll(".content-card");
  contentCards.forEach((card) => {
    card.style.opacity = "1";
    card.style.transform = "translateY(0)";
    card.style.transition = "opacity 0.6s ease, transform 0.6s ease";
  });
}

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Search functionality (for future implementation)
function initializeSearch() {
  // This can be expanded later to add search functionality
  console.log("Search functionality ready for implementation");
}

// Keyboard navigation
function initializeKeyboardNavigation() {
  document.addEventListener("keydown", function (e) {
    // Escape key to close sidebar
    if (e.key === "Escape") {
      const sidebar = document.getElementById("sidebar");
      const sidebarToggle = document.getElementById("sidebarToggle");

      if (sidebar && sidebar.classList.contains("active")) {
        sidebar.classList.remove("active");
        if (sidebarToggle) {
          sidebarToggle.classList.remove("active");
          const icon = sidebarToggle.querySelector("i");
          icon.className = "fas fa-bars";
        }
      }
    }

    // Ctrl/Cmd + K for search (future feature)
    if ((e.ctrlKey || e.metaKey) && e.key === "k") {
      e.preventDefault();
      // Future search implementation
      console.log("Search shortcut pressed");
    }
  });
}

// Initialize keyboard navigation
initializeKeyboardNavigation();

// Performance optimization: Lazy loading for images (future implementation)
function initializeLazyLoading() {
  // This can be expanded later to add lazy loading for images
  console.log("Lazy loading ready for implementation");
}

// Theme switching functionality (future implementation)
function initializeThemeSwitcher() {
  // This can be expanded later to add dark/light theme switching
  console.log("Theme switcher ready for implementation");
}

// Export functions for global access
window.DocumentationApp = {
  scrollToTop,
  initializeSearch,
  initializeLazyLoading,
  initializeThemeSwitcher,
};
