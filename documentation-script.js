// Documentation Website JavaScript
document.addEventListener("DOMContentLoaded", function () {
  // Initialize all functionality
  initializeNavigation();
  initializeSidebar();
  initializeMobileMenu();
  initializePDFExport();
  initializeSmoothScrolling();
  initializeActiveSectionTracking();
  initializeAnimations();
});

// Navigation functionality
function initializeNavigation() {
  const navLinks = document.querySelectorAll(".nav-link");
  const sections = document.querySelectorAll(".content-section");

  // Handle navigation clicks
  navLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();

      // Remove active class from all links
      navLinks.forEach((l) => l.classList.remove("active"));

      // Add active class to clicked link
      this.classList.add("active");

      // Get target section
      const targetId = this.getAttribute("href").substring(1);
      const targetSection = document.getElementById(targetId);

      if (targetSection) {
        // Smooth scroll to target
        targetSection.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });

        // Update URL without page reload
        history.pushState(null, null, `#${targetId}`);
      }
    });
  });
}

// Sidebar functionality
function initializeSidebar() {
  const sidebar = document.getElementById("sidebar");
  const sidebarToggle = document.getElementById("sidebarToggle");
  const mainContent = document.getElementById("mainContent");

  // Toggle sidebar on mobile
  if (sidebarToggle) {
    sidebarToggle.addEventListener("click", function () {
      sidebar.classList.toggle("active");
      this.classList.toggle("active");

      // Update toggle icon
      const icon = this.querySelector("i");
      if (sidebar.classList.contains("active")) {
        icon.className = "fas fa-times";
      } else {
        icon.className = "fas fa-bars";
      }
    });
  }

  // Close sidebar when clicking outside on mobile
  document.addEventListener("click", function (e) {
    if (window.innerWidth <= 1024) {
      if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
        sidebar.classList.remove("active");
        if (sidebarToggle) {
          sidebarToggle.classList.remove("active");
          const icon = sidebarToggle.querySelector("i");
          icon.className = "fas fa-bars";
        }
      }
    }
  });

  // Handle window resize
  window.addEventListener("resize", function () {
    if (window.innerWidth > 1024) {
      sidebar.classList.remove("active");
      if (sidebarToggle) {
        sidebarToggle.classList.remove("active");
        const icon = sidebarToggle.querySelector("i");
        icon.className = "fas fa-bars";
      }
    }
  });
}

// Mobile menu functionality
function initializeMobileMenu() {
  const mobileMenuToggle = document.getElementById("mobileMenuToggle");
  const sidebar = document.getElementById("sidebar");

  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener("click", function () {
      sidebar.classList.toggle("active");

      // Update toggle icon
      const icon = this.querySelector("i");
      if (sidebar.classList.contains("active")) {
        icon.className = "fas fa-times";
      } else {
        icon.className = "fas fa-bars";
      }
    });
  }
}

// PDF Export functionality
function initializePDFExport() {
  const pdfExportBtn = document.getElementById("pdfExportBtn");

  if (pdfExportBtn) {
    pdfExportBtn.addEventListener("click", function () {
      // Show loading overlay
      showPDFLoader();

      // Show loading state on button
      const originalText = this.innerHTML;
      this.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
      this.disabled = true;

      // Wait a bit for UI to update
      setTimeout(() => {
        generatePDF(originalText, this);
      }, 100);
    });
  }
}

// Show PDF loading overlay
function showPDFLoader() {
  // Create loader overlay
  const loader = document.createElement("div");
  loader.id = "pdf-loader";
  loader.innerHTML = `
    <div class="pdf-loader-content">
      <div class="pdf-loader-spinner">
        <i class="fas fa-file-pdf"></i>
        <div class="spinner-ring"></div>
      </div>
      <h3>Generating PDF...</h3>
      <p>Please wait while we create your documentation PDF</p>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
    </div>
  `;
  document.body.appendChild(loader);

  // Animate progress bar
  setTimeout(() => {
    const progressFill = loader.querySelector(".progress-fill");
    progressFill.style.width = "100%";
  }, 500);
}

// Hide PDF loading overlay
function hidePDFLoader() {
  const loader = document.getElementById("pdf-loader");
  if (loader) {
    loader.remove();
  }
}

// Generate PDF function using html2pdf.js (more robust than html2canvas + jsPDF)
function generatePDF(originalText, button) {
  // Get main content first so it's available for all uses
  const mainContent = document.getElementById("mainContent");

  // Check if mainContent exists and has content
  if (!mainContent) {
    console.error("mainContent element not found!");
    hidePDFLoader();
    button.innerHTML =
      '<i class="fas fa-exclamation-triangle"></i> Error: Content not found';
    setTimeout(() => {
      button.innerHTML = originalText;
      button.disabled = false;
    }, 3000);
    return;
  }

  console.log("mainContent found:", mainContent);
  console.log("mainContent dimensions:", {
    scrollWidth: mainContent.scrollWidth,
    scrollHeight: mainContent.scrollHeight,
    offsetWidth: mainContent.offsetWidth,
    offsetHeight: mainContent.offsetHeight,
  });

  // Check if html2pdf is available
  if (typeof html2pdf === "undefined") {
    console.error("html2pdf library not loaded!");
    hidePDFLoader();
    button.innerHTML =
      '<i class="fas fa-exclamation-triangle"></i> Error: PDF library not loaded';
    setTimeout(() => {
      button.innerHTML = originalText;
      button.disabled = false;
    }, 3000);
    return;
  }

  // Configure html2pdf options for better quality and compatibility
  const pdfOptions = {
    margin: [10, 10, 10, 10],
    filename: "Mass_Premier_Courts_Documentation.pdf",
    image: { type: "jpeg", quality: 0.98 },
    html2canvas: {
      scale: 2,
      useCORS: true,
      letterRendering: true,
      allowTaint: false,
      backgroundColor: "#ffffff",
      logging: false,
      width: mainContent.scrollWidth,
      height: mainContent.scrollHeight,
    },
    jsPDF: {
      unit: "mm",
      format: "a4",
      orientation: "portrait",
      compress: true,
    },
    pagebreak: { mode: ["avoid-all", "css", "legacy"] },
  };

  console.log("Starting PDF generation with html2pdf.js");
  console.log("PDF options:", pdfOptions);

  // Generate PDF using html2pdf.js
  html2pdf()
    .set(pdfOptions)
    .from(mainContent)
    .save()
    .then(() => {
      console.log("PDF generated successfully with html2pdf.js");
      // Restore everything
      restoreContentAfterPDF();
      hidePDFLoader();

      // Restore button state
      button.innerHTML = originalText;
      button.disabled = false;
    })
    .catch((error) => {
      console.error("PDF generation failed:", error);

      // Restore everything
      restoreContentAfterPDF();
      hidePDFLoader();

      // Show error message
      button.innerHTML =
        '<i class="fas fa-exclamation-triangle"></i> PDF Generation Failed';
      setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
      }, 3000);
    });
}

// Prepare content for PDF generation
function prepareContentForPDF() {
  // Force all animated elements to be visible
  const animatedSelectors = [
    ".feature-card",
    ".tech-item",
    ".report-card",
    ".export-type-card",
    ".filter-category",
    ".metric-card",
    ".auth-feature-card",
    ".role-card",
    ".control-card",
    ".measure-card",
    ".protection-feature-card",
  ];

  const animatedElements = document.querySelectorAll(
    animatedSelectors.join(", ")
  );
  animatedElements.forEach((el) => {
    el.style.opacity = "1";
    el.style.transform = "none";
    el.style.visibility = "visible";
  });

  // Ensure all content sections are visible
  const contentSections = document.querySelectorAll(".content-section");
  contentSections.forEach((section) => {
    section.style.opacity = "1";
    section.style.transform = "none";
    section.style.display = "block";
    section.style.visibility = "visible";
  });

  // Hide elements that shouldn't appear in PDF
  const elementsToHide = [
    "#sidebar",
    ".header-controls",
    ".section-nav",
    '.btn[onclick="scrollToTop()"]',
    ".mobile-menu-toggle",
    ".pdf-export-btn",
  ];

  elementsToHide.forEach((selector) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((el) => {
      el.style.display = "none";
    });
  });

  // Adjust main content for PDF
  const mainContent = document.getElementById("mainContent");
  if (mainContent) {
    mainContent.style.marginLeft = "0";
    mainContent.style.width = "100%";
    mainContent.style.background = "#ffffff";
  }
}

// Restore content after PDF generation
function restoreContentAfterPDF() {
  // This function can be expanded to restore original styles if needed
  // For now, we'll just ensure the page is in a good state
  const elementsToShow = ["#sidebar", ".header-controls", ".section-nav"];

  elementsToShow.forEach((selector) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((el) => {
      el.style.display = "";
    });
  });
}

// Restore original state function
function restoreOriginalState(
  sidebar,
  headerControls,
  sectionNavs,
  backToTopBtns,
  mainContent,
  originalSidebarDisplay,
  originalHeaderControlsDisplay,
  originalSectionNavDisplays,
  originalBackToTopDisplays,
  originalMarginLeft,
  originalWidth,
  button,
  originalText
) {
  sidebar.style.display = originalSidebarDisplay;
  headerControls.style.display = originalHeaderControlsDisplay;
  sectionNavs.forEach((nav, index) => {
    nav.style.display = originalSectionNavDisplays[index];
  });
  backToTopBtns.forEach((btn, index) => {
    btn.style.display = originalBackToTopDisplays[index];
  });
  mainContent.style.marginLeft = originalMarginLeft;
  mainContent.style.width = originalWidth;

  // Restore button state
  button.innerHTML = originalText;
  button.disabled = false;
}

// Smooth scrolling functionality
function initializeSmoothScrolling() {
  // Back to top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }

  // Make scrollToTop function globally available
  window.scrollToTop = scrollToTop;

  // Add scroll to top button functionality
  const scrollToTopButtons = document.querySelectorAll(
    '.btn[onclick="scrollToTop()"]'
  );
  scrollToTopButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();
      scrollToTop();
    });
  });
}

// Active section tracking
function initializeActiveSectionTracking() {
  const sections = document.querySelectorAll(".content-section");
  const navLinks = document.querySelectorAll(".nav-link");

  // Intersection Observer for tracking active sections
  const observerOptions = {
    root: null,
    rootMargin: "-20% 0px -70% 0px",
    threshold: 0,
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const currentId = entry.target.id;

        // Update navigation
        navLinks.forEach((link) => {
          link.classList.remove("active");
          if (link.getAttribute("href") === `#${currentId}`) {
            link.classList.add("active");
          }
        });

        // Update URL
        history.replaceState(null, null, `#${currentId}`);
      }
    });
  }, observerOptions);

  // Observe all sections
  sections.forEach((section) => {
    observer.observe(section);
  });

  // Handle initial page load with hash
  if (window.location.hash) {
    const targetSection = document.querySelector(window.location.hash);
    if (targetSection) {
      // Update active navigation
      navLinks.forEach((link) => {
        link.classList.remove("active");
        if (link.getAttribute("href") === window.location.hash) {
          link.classList.add("active");
        }
      });

      // Scroll to section
      setTimeout(() => {
        targetSection.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  }
}

// Animation functionality
function initializeAnimations() {
  // Intersection Observer for animations
  const animationObserver = new IntersectionObserver(
    function (entries) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = "1";
          entry.target.style.transform = "translateY(0)";
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    }
  );

  // Observe elements for animation (excluding content-card to prevent hiding)
  const animatedElements = document.querySelectorAll(
    ".tech-item, .feature-card, .report-card, .export-type-card, .filter-category, .metric-card, .auth-feature-card, .role-card, .control-card, .measure-card, .protection-feature-card"
  );
  animatedElements.forEach((el) => {
    el.style.opacity = "0";
    el.style.transform = "translateY(30px)";
    el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
    animationObserver.observe(el);
  });

  // Ensure all content cards are always visible
  const contentCards = document.querySelectorAll(".content-card");
  contentCards.forEach((card) => {
    card.style.opacity = "1";
    card.style.transform = "translateY(0)";
    card.style.transition = "opacity 0.6s ease, transform 0.6s ease";
  });
}

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Search functionality (for future implementation)
function initializeSearch() {
  // This can be expanded later to add search functionality
  console.log("Search functionality ready for implementation");
}

// Keyboard navigation
function initializeKeyboardNavigation() {
  document.addEventListener("keydown", function (e) {
    // Escape key to close sidebar
    if (e.key === "Escape") {
      const sidebar = document.getElementById("sidebar");
      const sidebarToggle = document.getElementById("sidebarToggle");

      if (sidebar && sidebar.classList.contains("active")) {
        sidebar.classList.remove("active");
        if (sidebarToggle) {
          sidebarToggle.classList.remove("active");
          const icon = sidebarToggle.querySelector("i");
          icon.className = "fas fa-bars";
        }
      }
    }

    // Ctrl/Cmd + K for search (future feature)
    if ((e.ctrlKey || e.metaKey) && e.key === "k") {
      e.preventDefault();
      // Future search implementation
      console.log("Search shortcut pressed");
    }
  });
}

// Initialize keyboard navigation
initializeKeyboardNavigation();

// Performance optimization: Lazy loading for images (future implementation)
function initializeLazyLoading() {
  // This can be expanded later to add lazy loading for images
  console.log("Lazy loading ready for implementation");
}

// Theme switching functionality (future implementation)
function initializeThemeSwitcher() {
  // This can be expanded later to add dark/light theme switching
  console.log("Theme switcher ready for implementation");
}

// Export functions for global access
window.DocumentationApp = {
  scrollToTop,
  initializeSearch,
  initializeLazyLoading,
  initializeThemeSwitcher,
};
