// Documentation Website JavaScript
document.addEventListener("DOMContentLoaded", function () {
  // Initialize all functionality
  initializeNavigation();
  initializeSidebar();
  initializeMobileMenu();
  initializePDFExport();
  initializeSmoothScrolling();
  initializeActiveSectionTracking();
  initializeAnimations();
});

// Navigation functionality
function initializeNavigation() {
  const navLinks = document.querySelectorAll(".nav-link");
  const sections = document.querySelectorAll(".content-section");

  // Handle navigation clicks
  navLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();

      // Remove active class from all links
      navLinks.forEach((l) => l.classList.remove("active"));

      // Add active class to clicked link
      this.classList.add("active");

      // Get target section
      const targetId = this.getAttribute("href").substring(1);
      const targetSection = document.getElementById(targetId);

      if (targetSection) {
        // Smooth scroll to target
        targetSection.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });

        // Update URL without page reload
        history.pushState(null, null, `#${targetId}`);
      }
    });
  });
}

// Sidebar functionality
function initializeSidebar() {
  const sidebar = document.getElementById("sidebar");
  const sidebarToggle = document.getElementById("sidebarToggle");
  const mainContent = document.getElementById("mainContent");

  // Toggle sidebar on mobile
  if (sidebarToggle) {
    sidebarToggle.addEventListener("click", function () {
      sidebar.classList.toggle("active");
      this.classList.toggle("active");

      // Update toggle icon
      const icon = this.querySelector("i");
      if (sidebar.classList.contains("active")) {
        icon.className = "fas fa-times";
      } else {
        icon.className = "fas fa-bars";
      }
    });
  }

  // Close sidebar when clicking outside on mobile
  document.addEventListener("click", function (e) {
    if (window.innerWidth <= 1024) {
      if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
        sidebar.classList.remove("active");
        if (sidebarToggle) {
          sidebarToggle.classList.remove("active");
          const icon = sidebarToggle.querySelector("i");
          icon.className = "fas fa-bars";
        }
      }
    }
  });

  // Handle window resize
  window.addEventListener("resize", function () {
    if (window.innerWidth > 1024) {
      sidebar.classList.remove("active");
      if (sidebarToggle) {
        sidebarToggle.classList.remove("active");
        const icon = sidebarToggle.querySelector("i");
        icon.className = "fas fa-bars";
      }
    }
  });
}

// Mobile menu functionality
function initializeMobileMenu() {
  const mobileMenuToggle = document.getElementById("mobileMenuToggle");
  const sidebar = document.getElementById("sidebar");

  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener("click", function () {
      sidebar.classList.toggle("active");

      // Update toggle icon
      const icon = this.querySelector("i");
      if (sidebar.classList.contains("active")) {
        icon.className = "fas fa-times";
      } else {
        icon.className = "fas fa-bars";
      }
    });
  }
}

// PDF Export functionality
function initializePDFExport() {
  const pdfExportBtn = document.getElementById("pdfExportBtn");

  if (pdfExportBtn) {
    pdfExportBtn.addEventListener("click", function () {
      // Show loading overlay
      showPDFLoader();

      // Show loading state on button
      const originalText = this.innerHTML;
      this.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
      this.disabled = true;

      // Wait a bit for UI to update
      setTimeout(() => {
        generatePDF(originalText, this);
      }, 100);
    });
  }
}

// Show PDF loading overlay
function showPDFLoader() {
  // Create loader overlay
  const loader = document.createElement("div");
  loader.id = "pdf-loader";
  loader.innerHTML = `
    <div class="pdf-loader-content">
      <div class="pdf-loader-spinner">
        <i class="fas fa-file-pdf"></i>
        <div class="spinner-ring"></div>
      </div>
      <h3>Generating PDF...</h3>
      <p>Please wait while we create your documentation PDF</p>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
    </div>
  `;
  document.body.appendChild(loader);

  // Animate progress bar
  setTimeout(() => {
    const progressFill = loader.querySelector(".progress-fill");
    progressFill.style.width = "100%";
  }, 500);
}

// Hide PDF loading overlay
function hidePDFLoader() {
  const loader = document.getElementById("pdf-loader");
  if (loader) {
    loader.remove();
  }
}

// Generate PDF function using html2pdf.js (more robust than html2canvas + jsPDF)
function generatePDF(originalText, button) {
  // Get main content first so it's available for all uses
  const mainContent = document.getElementById("mainContent");

  // Check if mainContent exists and has content
  if (!mainContent) {
    console.error("mainContent element not found!");
    hidePDFLoader();
    button.innerHTML =
      '<i class="fas fa-exclamation-triangle"></i> Error: Content not found';
    setTimeout(() => {
      button.innerHTML = originalText;
      button.disabled = false;
    }, 3000);
    return;
  }

  console.log("mainContent found:", mainContent);
  console.log("mainContent dimensions:", {
    scrollWidth: mainContent.scrollWidth,
    scrollHeight: mainContent.scrollHeight,
    offsetWidth: mainContent.offsetWidth,
    offsetHeight: mainContent.offsetHeight,
  });

  // Check if PDF libraries are available and try different approaches
  if (typeof html2pdf === "undefined" && typeof pdfMake === "undefined") {
    console.error("No PDF libraries loaded!");
    hidePDFLoader();
    button.innerHTML =
      '<i class="fas fa-exclamation-triangle"></i> Error: PDF library not loaded';
    setTimeout(() => {
      button.innerHTML = originalText;
      button.disabled = false;
    }, 3000);
    return;
  }

  // Use html2pdf.js to preserve original CSS styling
  console.log("Using html2pdf.js to preserve original CSS styling");

  // Prepare content for better PDF rendering
  prepareContentForPDF();

  // Configure html2pdf options to preserve original CSS styling
  const pdfOptions = {
    margin: [15, 15, 15, 15],
    filename: "Mass_Premier_Courts_Documentation.pdf",
    image: { type: "jpeg", quality: 0.95 },
    html2canvas: {
      scale: 1.5,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      logging: true,
      width: mainContent.scrollWidth,
      height: mainContent.scrollHeight,
      scrollX: 0,
      scrollY: 0,
      windowWidth: 1200,
      windowHeight: window.innerHeight,
      onclone: function (clonedDoc) {
        console.log("Document cloned for PDF generation");

        // Ensure all stylesheets are loaded in the clone
        const originalStylesheets = document.querySelectorAll(
          'link[rel="stylesheet"], style'
        );
        originalStylesheets.forEach((stylesheet) => {
          if (
            !clonedDoc.querySelector(`[href="${stylesheet.href}"]`) &&
            !clonedDoc.contains(stylesheet)
          ) {
            clonedDoc.head.appendChild(stylesheet.cloneNode(true));
          }
        });

        // Add additional CSS to ensure proper PDF rendering
        const pdfStyles = clonedDoc.createElement("style");
        pdfStyles.textContent = `
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }

          .main-content {
            margin-left: 0 !important;
            width: 100% !important;
            background: #ffffff !important;
          }

          .feature-card, .tech-item, .report-card, .export-type-card,
          .filter-category, .metric-card, .auth-feature-card, .role-card,
          .control-card, .measure-card, .protection-feature-card {
            opacity: 1 !important;
            transform: none !important;
            visibility: visible !important;
            display: block !important;
            page-break-inside: avoid !important;
          }

          .content-section {
            opacity: 1 !important;
            transform: none !important;
            display: block !important;
            visibility: visible !important;
          }

          .tech-grid, .features-grid {
            display: grid !important;
          }

          h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid !important;
          }

          .content-card {
            page-break-inside: avoid !important;
          }
        `;
        clonedDoc.head.appendChild(pdfStyles);

        // Ensure the cloned main content is properly styled
        const clonedMainContent = clonedDoc.getElementById("mainContent");
        if (clonedMainContent) {
          clonedMainContent.style.display = "block";
          clonedMainContent.style.visibility = "visible";
          clonedMainContent.style.opacity = "1";
          clonedMainContent.style.background = "#ffffff";
          clonedMainContent.style.marginLeft = "0";
          clonedMainContent.style.width = "100%";

          // Force all animated elements to be visible with original styles
          const animatedElements = clonedMainContent.querySelectorAll(
            ".feature-card, .tech-item, .report-card, .export-type-card, .filter-category, .metric-card, .auth-feature-card, .role-card, .control-card, .measure-card, .protection-feature-card"
          );
          animatedElements.forEach((el) => {
            el.style.opacity = "1";
            el.style.transform = "none";
            el.style.visibility = "visible";
          });

          // Ensure all content sections are visible
          const contentSections =
            clonedMainContent.querySelectorAll(".content-section");
          contentSections.forEach((section) => {
            section.style.opacity = "1";
            section.style.transform = "none";
            section.style.display = "block";
            section.style.visibility = "visible";
          });
        }
      },
    },
    jsPDF: {
      unit: "mm",
      format: "a4",
      orientation: "portrait",
      compress: true,
    },
    pagebreak: {
      mode: ["avoid-all", "css", "legacy"],
      before: ".content-section",
      after: ".content-section",
      avoid: ".feature-card, .tech-item",
    },
  };

  console.log("Starting PDF generation with html2pdf.js");
  console.log("PDF options:", pdfOptions);

  // Generate PDF using html2pdf.js with better debugging
  html2pdf()
    .set(pdfOptions)
    .from(mainContent)
    .toPdf()
    .get("pdf")
    .then((pdf) => {
      console.log("PDF object created:", pdf);
      console.log("PDF page count:", pdf.internal.getNumberOfPages());

      // Check if PDF has content
      const pdfData = pdf.output("datauristring");
      console.log("PDF data length:", pdfData.length);

      if (pdfData.length < 1000) {
        throw new Error("PDF appears to be empty or too small");
      }

      // Save the PDF
      pdf.save("Mass_Premier_Courts_Documentation.pdf");
      console.log("PDF generated successfully with html2pdf.js");

      // Restore everything
      restoreContentAfterPDF();
      hidePDFLoader();

      // Restore button state
      button.innerHTML = originalText;
      button.disabled = false;
    })
    .catch((error) => {
      console.error("PDF generation failed:", error);

      // Restore everything
      restoreContentAfterPDF();
      hidePDFLoader();

      // Show error message
      button.innerHTML =
        '<i class="fas fa-exclamation-triangle"></i> PDF Generation Failed';
      setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
      }, 3000);
    });
}

// Generate PDF using PDFMake (more reliable approach)
function generatePDFWithPDFMake(mainContent, originalText, button) {
  console.log("Starting PDF generation with PDFMake");

  try {
    // Extract text content from the main content
    const documentDefinition = createPDFDocumentDefinition(mainContent);

    console.log("Document definition created:", documentDefinition);

    // Generate and download PDF
    pdfMake
      .createPdf(documentDefinition)
      .download("Mass_Premier_Courts_Documentation.pdf");

    console.log("PDF generated successfully with PDFMake");

    // Restore everything
    restoreContentAfterPDF();
    hidePDFLoader();

    // Restore button state
    button.innerHTML = originalText;
    button.disabled = false;
  } catch (error) {
    console.error("PDFMake generation failed:", error);

    // Restore everything
    restoreContentAfterPDF();
    hidePDFLoader();

    // Show error message
    button.innerHTML =
      '<i class="fas fa-exclamation-triangle"></i> PDF Generation Failed';
    setTimeout(() => {
      button.innerHTML = originalText;
      button.disabled = false;
    }, 3000);
  }
}

// Create PDF document definition from HTML content with enhanced styling
function createPDFDocumentDefinition(mainContent) {
  const content = [];

  // Add styled title with background
  content.push({
    table: {
      widths: ["*"],
      body: [
        [
          {
            text: "Mass Premier Courts - Sports Program Management System",
            style: "header",
            fillColor: "#3498db",
            color: "white",
            margin: [15, 15, 15, 15],
          },
        ],
      ],
    },
    layout: "noBorders",
    margin: [0, 0, 0, 10],
  });

  content.push({
    text: "Comprehensive Documentation & Technical Guide",
    style: "subheader",
    alignment: "center",
    margin: [0, 0, 0, 30],
  });

  // Extract content from sections
  const sections = mainContent.querySelectorAll(".content-section");

  sections.forEach((section) => {
    // Add section title with styling
    const sectionTitle = section.querySelector("h2");
    if (sectionTitle) {
      const titleText = sectionTitle.textContent
        .replace(/^\s*[\u2022\u25E6\u25AA]\s*/, "")
        .trim();

      content.push({
        table: {
          widths: ["*"],
          body: [
            [
              {
                text: titleText,
                style: "sectionHeader",
                fillColor: "#ecf0f1",
                margin: [10, 8, 10, 8],
              },
            ],
          ],
        },
        layout: "noBorders",
        margin: [0, 20, 0, 15],
      });
    }

    // Add lead paragraphs with emphasis
    const leadParagraphs = section.querySelectorAll(".lead");
    leadParagraphs.forEach((p) => {
      if (p.textContent.trim()) {
        content.push({
          text: p.textContent.trim(),
          style: "lead",
          margin: [0, 0, 0, 15],
        });
      }
    });

    // Add regular paragraphs
    const paragraphs = section.querySelectorAll("p:not(.lead)");
    paragraphs.forEach((p) => {
      if (p.textContent.trim()) {
        content.push({
          text: p.textContent.trim(),
          style: "normal",
          margin: [0, 0, 0, 10],
        });
      }
    });

    // Add tech stack with enhanced formatting
    const techGrid = section.querySelector(".tech-grid");
    if (techGrid) {
      const techItems = techGrid.querySelectorAll(".tech-item");
      if (techItems.length > 0) {
        content.push({
          text: "Core Technologies",
          style: "subSectionHeader",
          margin: [0, 15, 0, 10],
        });

        const techTable = [];
        const techRow = [];

        techItems.forEach((item, index) => {
          const title = item.querySelector("h4");
          const desc = item.querySelector("p");
          if (title && desc) {
            techRow.push({
              text: [
                { text: title.textContent.trim() + "\n", style: "techTitle" },
                { text: desc.textContent.trim(), style: "techDesc" },
              ],
              margin: [5, 5, 5, 5],
              fillColor: index % 2 === 0 ? "#f8f9fa" : "#ffffff",
            });

            if (techRow.length === 2 || index === techItems.length - 1) {
              techTable.push([...techRow]);
              techRow.length = 0;
            }
          }
        });

        if (techTable.length > 0) {
          content.push({
            table: {
              widths: ["*", "*"],
              body: techTable,
            },
            layout: {
              hLineWidth: () => 0.5,
              vLineWidth: () => 0.5,
              hLineColor: () => "#dee2e6",
              vLineColor: () => "#dee2e6",
            },
            margin: [0, 0, 0, 20],
          });
        }
      }
    }

    // Add feature cards with enhanced styling
    const featureCards = section.querySelectorAll(".feature-card");
    if (featureCards.length > 0) {
      content.push({
        text: "Key Features",
        style: "subSectionHeader",
        margin: [0, 15, 0, 10],
      });

      featureCards.forEach((card, index) => {
        const title = card.querySelector("h4");
        const desc = card.querySelector("p");
        if (title && desc) {
          content.push({
            table: {
              widths: ["*"],
              body: [
                [
                  {
                    text: [
                      {
                        text: title.textContent.trim() + "\n",
                        style: "featureTitle",
                      },
                      { text: desc.textContent.trim(), style: "featureDesc" },
                    ],
                    margin: [10, 8, 10, 8],
                    fillColor: "#f8f9fa",
                  },
                ],
              ],
            },
            layout: {
              hLineWidth: () => 1,
              vLineWidth: () => 1,
              hLineColor: () => "#3498db",
              vLineColor: () => "#3498db",
            },
            margin: [0, 5, 0, 5],
          });
        }
      });

      content.push({ text: "", margin: [0, 0, 0, 15] }); // Spacing after features
    }

    // Add other card types (report cards, metric cards, etc.)
    const otherCards = section.querySelectorAll(
      ".report-card, .metric-card, .auth-feature-card, .role-card, .control-card, .measure-card, .protection-feature-card"
    );
    if (otherCards.length > 0) {
      const cardList = [];
      otherCards.forEach((card) => {
        const title = card.querySelector("h4, h3");
        const desc = card.querySelector("p");
        if (title && desc) {
          cardList.push({
            text: [
              {
                text: "• " + title.textContent.trim() + ": ",
                style: "listTitle",
              },
              { text: desc.textContent.trim(), style: "listDesc" },
            ],
            margin: [0, 3, 0, 3],
          });
        }
      });

      if (cardList.length > 0) {
        content.push({
          stack: cardList,
          margin: [20, 10, 0, 20],
        });
      }
    }
  });

  return {
    content: content,
    styles: {
      header: {
        fontSize: 20,
        bold: true,
        alignment: "center",
      },
      subheader: {
        fontSize: 14,
        italics: true,
        color: "#7f8c8d",
      },
      sectionHeader: {
        fontSize: 16,
        bold: true,
        color: "#2c3e50",
      },
      subSectionHeader: {
        fontSize: 14,
        bold: true,
        color: "#34495e",
      },
      lead: {
        fontSize: 12,
        lineHeight: 1.5,
        color: "#2c3e50",
        italics: true,
      },
      normal: {
        fontSize: 11,
        lineHeight: 1.4,
        color: "#2c3e50",
      },
      techTitle: {
        fontSize: 12,
        bold: true,
        color: "#3498db",
      },
      techDesc: {
        fontSize: 10,
        color: "#7f8c8d",
      },
      featureTitle: {
        fontSize: 12,
        bold: true,
        color: "#e74c3c",
      },
      featureDesc: {
        fontSize: 10,
        color: "#2c3e50",
      },
      listTitle: {
        fontSize: 11,
        bold: true,
        color: "#3498db",
      },
      listDesc: {
        fontSize: 10,
        color: "#2c3e50",
      },
    },
    defaultStyle: {
      fontSize: 11,
      lineHeight: 1.3,
      color: "#2c3e50",
    },
    pageMargins: [40, 60, 40, 60],
    info: {
      title: "Mass Premier Courts Documentation",
      author: "Mass Premier Courts",
      subject: "Sports Program Management System Documentation",
    },
  };
}

// Prepare content for PDF generation
function prepareContentForPDF() {
  // Force all animated elements to be visible
  const animatedSelectors = [
    ".feature-card",
    ".tech-item",
    ".report-card",
    ".export-type-card",
    ".filter-category",
    ".metric-card",
    ".auth-feature-card",
    ".role-card",
    ".control-card",
    ".measure-card",
    ".protection-feature-card",
  ];

  const animatedElements = document.querySelectorAll(
    animatedSelectors.join(", ")
  );
  animatedElements.forEach((el) => {
    el.style.opacity = "1";
    el.style.transform = "none";
    el.style.visibility = "visible";
  });

  // Ensure all content sections are visible
  const contentSections = document.querySelectorAll(".content-section");
  contentSections.forEach((section) => {
    section.style.opacity = "1";
    section.style.transform = "none";
    section.style.display = "block";
    section.style.visibility = "visible";
  });

  // Hide elements that shouldn't appear in PDF
  const elementsToHide = [
    "#sidebar",
    ".header-controls",
    ".section-nav",
    '.btn[onclick="scrollToTop()"]',
    ".mobile-menu-toggle",
    ".pdf-export-btn",
  ];

  elementsToHide.forEach((selector) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((el) => {
      el.style.display = "none";
    });
  });

  // Adjust main content for PDF
  const mainContent = document.getElementById("mainContent");
  if (mainContent) {
    mainContent.style.marginLeft = "0";
    mainContent.style.width = "100%";
    mainContent.style.background = "#ffffff";
  }
}

// Restore content after PDF generation
function restoreContentAfterPDF() {
  // This function can be expanded to restore original styles if needed
  // For now, we'll just ensure the page is in a good state
  const elementsToShow = ["#sidebar", ".header-controls", ".section-nav"];

  elementsToShow.forEach((selector) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((el) => {
      el.style.display = "";
    });
  });
}

// Restore original state function
function restoreOriginalState(
  sidebar,
  headerControls,
  sectionNavs,
  backToTopBtns,
  mainContent,
  originalSidebarDisplay,
  originalHeaderControlsDisplay,
  originalSectionNavDisplays,
  originalBackToTopDisplays,
  originalMarginLeft,
  originalWidth,
  button,
  originalText
) {
  sidebar.style.display = originalSidebarDisplay;
  headerControls.style.display = originalHeaderControlsDisplay;
  sectionNavs.forEach((nav, index) => {
    nav.style.display = originalSectionNavDisplays[index];
  });
  backToTopBtns.forEach((btn, index) => {
    btn.style.display = originalBackToTopDisplays[index];
  });
  mainContent.style.marginLeft = originalMarginLeft;
  mainContent.style.width = originalWidth;

  // Restore button state
  button.innerHTML = originalText;
  button.disabled = false;
}

// Smooth scrolling functionality
function initializeSmoothScrolling() {
  // Back to top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }

  // Make scrollToTop function globally available
  window.scrollToTop = scrollToTop;

  // Add scroll to top button functionality
  const scrollToTopButtons = document.querySelectorAll(
    '.btn[onclick="scrollToTop()"]'
  );
  scrollToTopButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();
      scrollToTop();
    });
  });
}

// Active section tracking
function initializeActiveSectionTracking() {
  const sections = document.querySelectorAll(".content-section");
  const navLinks = document.querySelectorAll(".nav-link");

  // Intersection Observer for tracking active sections
  const observerOptions = {
    root: null,
    rootMargin: "-20% 0px -70% 0px",
    threshold: 0,
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const currentId = entry.target.id;

        // Update navigation
        navLinks.forEach((link) => {
          link.classList.remove("active");
          if (link.getAttribute("href") === `#${currentId}`) {
            link.classList.add("active");
          }
        });

        // Update URL
        history.replaceState(null, null, `#${currentId}`);
      }
    });
  }, observerOptions);

  // Observe all sections
  sections.forEach((section) => {
    observer.observe(section);
  });

  // Handle initial page load with hash
  if (window.location.hash) {
    const targetSection = document.querySelector(window.location.hash);
    if (targetSection) {
      // Update active navigation
      navLinks.forEach((link) => {
        link.classList.remove("active");
        if (link.getAttribute("href") === window.location.hash) {
          link.classList.add("active");
        }
      });

      // Scroll to section
      setTimeout(() => {
        targetSection.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  }
}

// Animation functionality
function initializeAnimations() {
  // Intersection Observer for animations
  const animationObserver = new IntersectionObserver(
    function (entries) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = "1";
          entry.target.style.transform = "translateY(0)";
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    }
  );

  // Observe elements for animation (excluding content-card to prevent hiding)
  const animatedElements = document.querySelectorAll(
    ".tech-item, .feature-card, .report-card, .export-type-card, .filter-category, .metric-card, .auth-feature-card, .role-card, .control-card, .measure-card, .protection-feature-card"
  );
  animatedElements.forEach((el) => {
    el.style.opacity = "0";
    el.style.transform = "translateY(30px)";
    el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
    animationObserver.observe(el);
  });

  // Ensure all content cards are always visible
  const contentCards = document.querySelectorAll(".content-card");
  contentCards.forEach((card) => {
    card.style.opacity = "1";
    card.style.transform = "translateY(0)";
    card.style.transition = "opacity 0.6s ease, transform 0.6s ease";
  });
}

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Search functionality (for future implementation)
function initializeSearch() {
  // This can be expanded later to add search functionality
  console.log("Search functionality ready for implementation");
}

// Keyboard navigation
function initializeKeyboardNavigation() {
  document.addEventListener("keydown", function (e) {
    // Escape key to close sidebar
    if (e.key === "Escape") {
      const sidebar = document.getElementById("sidebar");
      const sidebarToggle = document.getElementById("sidebarToggle");

      if (sidebar && sidebar.classList.contains("active")) {
        sidebar.classList.remove("active");
        if (sidebarToggle) {
          sidebarToggle.classList.remove("active");
          const icon = sidebarToggle.querySelector("i");
          icon.className = "fas fa-bars";
        }
      }
    }

    // Ctrl/Cmd + K for search (future feature)
    if ((e.ctrlKey || e.metaKey) && e.key === "k") {
      e.preventDefault();
      // Future search implementation
      console.log("Search shortcut pressed");
    }
  });
}

// Initialize keyboard navigation
initializeKeyboardNavigation();

// Performance optimization: Lazy loading for images (future implementation)
function initializeLazyLoading() {
  // This can be expanded later to add lazy loading for images
  console.log("Lazy loading ready for implementation");
}

// Theme switching functionality (future implementation)
function initializeThemeSwitcher() {
  // This can be expanded later to add dark/light theme switching
  console.log("Theme switcher ready for implementation");
}

// Export functions for global access
window.DocumentationApp = {
  scrollToTop,
  initializeSearch,
  initializeLazyLoading,
  initializeThemeSwitcher,
};
